<template>
  <CustomPopup 
    :show="show" 
    mode="bottom"
    :round="24"
    :close-on-click-overlay="false"
    @close="handleCancel"
  >
    <view class="custom-picker">
      <!-- 头部 -->
      <view class="picker-header">
        <view class="picker-cancel" @click="handleCancel">取消</view>
        <view class="picker-title">{{ title }}</view>
        <view class="picker-confirm" @click="handleConfirm">确定</view>
      </view>
      
      <!-- 选择器内容 -->
      <view class="picker-content">
        <picker-view 
          class="picker-view"
          :value="currentIndex"
          @change="handlePickerChange"
        >
          <picker-view-column v-for="(column, columnIndex) in columns" :key="columnIndex">
            <view 
              v-for="(item, itemIndex) in column" 
              :key="itemIndex"
              class="picker-item"
            >
              {{ getItemLabel(item) }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '请选择'
  },
  columns: {
    type: Array,
    default: () => [[]]
  },
  keyName: {
    type: String,
    default: 'label'
  },
  valueName: {
    type: String,
    default: 'value'
  },
  defaultIndex: {
    type: Array,
    default: () => [0]
  }
})

const emit = defineEmits(['confirm', 'cancel', 'change'])

// 当前选中的索引
const currentIndex = ref([...props.defaultIndex])

// 监听默认索引变化
watch(() => props.defaultIndex, (newVal) => {
  currentIndex.value = [...newVal]
}, { deep: true })

// 监听显示状态变化，重置索引
watch(() => props.show, (newVal) => {
  if (newVal) {
    currentIndex.value = [...props.defaultIndex]
  }
})

// 获取项目标签
const getItemLabel = (item) => {
  if (typeof item === 'string') return item
  if (typeof item === 'object' && item !== null) {
    return item[props.keyName] || item.label || item.name || String(item)
  }
  return String(item)
}

// 获取项目值
const getItemValue = (item) => {
  if (typeof item === 'string') return item
  if (typeof item === 'object' && item !== null) {
    return item[props.valueName] || item.value || item
  }
  return item
}

// 获取当前选中的值
const getCurrentValues = () => {
  return currentIndex.value.map((index, columnIndex) => {
    const column = props.columns[columnIndex] || []
    const item = column[index] || column[0]
    return {
      label: getItemLabel(item),
      value: getItemValue(item),
      index: index
    }
  })
}

// 处理选择器变化
const handlePickerChange = (event) => {
  currentIndex.value = event.detail.value
  emit('change', {
    value: getCurrentValues(),
    index: currentIndex.value
  })
}

// 处理确认
const handleConfirm = () => {
  const values = getCurrentValues()
  emit('confirm', {
    value: values,
    index: currentIndex.value
  })
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.custom-picker {
  background: white;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  padding: 16rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.picker-cancel {
  color: #666;
}

.picker-cancel:active {
  background: #f5f5f5;
  border-radius: 8rpx;
}

.picker-confirm {
  color: #1890ff;
  font-weight: 500;
}

.picker-confirm:active {
  background: #f0f8ff;
  border-radius: 8rpx;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  flex: 1;
}

.picker-content {
  height: 500rpx;
  background: white;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  font-size: 28rpx;
  color: #000;
  line-height: 1.4;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .picker-header {
    height: 96rpx;
  }
  
  .picker-cancel,
  .picker-confirm {
    font-size: 30rpx;
  }
  
  .picker-title {
    font-size: 32rpx;
  }
  
  .picker-item {
    height: 96rpx;
    font-size: 30rpx;
  }
  
  .picker-content {
    height: 540rpx;
  }
}
</style>

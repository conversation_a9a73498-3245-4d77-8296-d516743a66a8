<template>
  <view class="upload-container">
    <view class="title" v-if="title"><span v-if="required" class="required">*</span> {{ title }}</view>
    <view class="content">
      <up-upload
          class="custom-upload"
          :fileList="localFileList"
          @afterRead="handleAfterRead"
          @delete="handleDelete"
          name="1"
          multiple
          :maxCount="disabled ? fileList.length : maxCount"
          :disabled="disabled"
          :capture="capture"
      />
    </view>
  </view>
</template>

<script setup>
import {ref, watch} from 'vue'
import * as uploadApi from '@/api/upload'

// 接收父组件传入的 fileList
const props = defineProps({
  fileList: { // 默认数据
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: ""
  },
  maxCount: { // 最大上传数
    type: Number,
    default: 9
  },
  disabled: { // 是否允许上传
    type: Boolean,
    default: false
  },
  required: { // 是否显示必填得*号
    type: Boolean,
    default: false
  }
})

// 向父组件发送事件
const emit = defineEmits(['update:fileList'])
// 上传类型
const capture = ref(["camera"])

// 内部维护的文件列表
const localFileList = ref([...props.fileList])

// 监听 props 变化，同步到内部
watch(() => props.fileList, (newVal) => {
  localFileList.value = [...newVal]
})

// 删除图片
const handleDelete = (event) => {
  if (props.disabled) return
  localFileList.value.splice(event.index, 1)
  emit('update:fileList', localFileList.value)
}

// 新增图片
const handleAfterRead = async (event) => {
  // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
  let newFiles = [].concat(event.file)
  let fileListLen = localFileList.value.length
  
  // 添加所有文件到列表，设置为上传中状态
  newFiles.map((item) => {
    localFileList.value.push({
      ...item,
      status: 'uploading',
      message: '上传中',
    });
  });
  
  // 通知父组件更新状态
  emit('update:fileList', localFileList.value)

  // 逐个上传图片
  for (let i = 0; i < newFiles.length; i++) {
    const index = fileListLen + i
    try {
      const result = await uploadApi.uploadFile(newFiles[i].url)
      
      // 检查上传结果
      if (result[0]['code'] === 200) {
        const fileUrl = result[0]['data']['file']['url']
        // 如果上传成功，更新状态为成功
        if (index < localFileList.value.length) {
          localFileList.value[index] = {
            // ...localFileList.value[index],
            status: 'success',
            url: fileUrl,
          }
        }
      } else {
        // 上传失败，更新状态或移除
        if (index < localFileList.value.length) {
          // 方式1：更新状态为失败
          localFileList.value[index] = {
            ...localFileList.value[index],
            status: 'error',
            message: '上传失败',
          }
          
          // 显示失败信息后删除
          setTimeout(() => {
            // 检查索引是否仍然有效
            const currentIndex = localFileList.value.findIndex(
              file => file.url === newFiles[i].url && file.status === 'error'
            )
            if (currentIndex !== -1) {
              localFileList.value.splice(currentIndex, 1)
              // 通知父组件
              emit('update:fileList', localFileList.value)
            }
          }, 1500)
        }
      }
    } catch (error) {
      console.error('上传文件失败:', error)
      // 发生异常，更新状态为失败
      if (index < localFileList.value.length) {
        // 更新状态为失败
        localFileList.value[index] = {
          ...localFileList.value[index],
          status: 'error',
          message: '上传失败: ' + (error.message || '未知错误'),
        }
        
        // 显示失败信息后删除
        setTimeout(() => {
          // 检查索引是否仍然有效
          const currentIndex = localFileList.value.findIndex(
            file => file.url === newFiles[i].url && file.status === 'error'
          )
          if (currentIndex !== -1) {
            localFileList.value.splice(currentIndex, 1)
            // 通知父组件
            emit('update:fileList', localFileList.value)
          }
        }, 1500)
      }
    }
    
    // 每次上传后都通知父组件更新状态
    emit('update:fileList', localFileList.value)
  }
}
</script>

<style scoped>
.upload-container {
  width: 100%;
}

.title {
  line-height: 40rpx;
  margin: 10rpx 0;
}

.content {
  background: white;
  padding: 20rpx 0;
}

.custom-upload {
  padding: 0 6rpx;
}

.required {
  color: #f56c6c;
  line-height: 40rpx;
}
</style>

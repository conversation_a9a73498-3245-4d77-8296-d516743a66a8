<template>
  <view class="custom-number-box" :class="{ 'number-disabled': disabled }">
    <!-- 减少按钮 -->
    <view 
      class="number-btn decrease-btn"
      :class="{ 'btn-disabled': isDecreaseDisabled }"
      @click="decrease"
    >
      <text class="btn-icon">−</text>
    </view>
    
    <!-- 数字输入框 -->
    <input
      class="number-input"
      type="number"
      :value="displayValue"
      :disabled="disabled"
      :style="{ width: inputWidth }"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    
    <!-- 增加按钮 -->
    <view 
      class="number-btn increase-btn"
      :class="{ 'btn-disabled': isIncreaseDisabled }"
      @click="increase"
    >
      <text class="btn-icon">+</text>
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 0
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: Infinity
  },
  step: {
    type: Number,
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false
  },
  integer: {
    type: Boolean,
    default: false
  },
  inputWidth: {
    type: String,
    default: '100rpx'
  },
  precision: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur'])

const isFocused = ref(false)

// 当前值
const currentValue = computed(() => {
  const value = Number(props.modelValue)
  return isNaN(value) ? props.min : value
})

// 显示值
const displayValue = computed(() => {
  if (props.integer) {
    return Math.floor(currentValue.value).toString()
  }
  
  if (props.precision > 0) {
    return currentValue.value.toFixed(props.precision)
  }
  
  return currentValue.value.toString()
})

// 是否可以减少
const isDecreaseDisabled = computed(() => {
  return props.disabled || currentValue.value <= props.min
})

// 是否可以增加
const isIncreaseDisabled = computed(() => {
  return props.disabled || currentValue.value >= props.max
})

// 格式化数值
const formatValue = (value) => {
  let num = Number(value)
  
  if (isNaN(num)) {
    num = props.min
  }
  
  // 限制范围
  num = Math.max(props.min, Math.min(props.max, num))
  
  // 整数处理
  if (props.integer) {
    num = Math.floor(num)
  } else if (props.precision > 0) {
    num = Number(num.toFixed(props.precision))
  }
  
  return num
}

// 更新值
const updateValue = (newValue) => {
  const formattedValue = formatValue(newValue)
  if (formattedValue !== currentValue.value) {
    emit('update:modelValue', formattedValue)
    emit('change', formattedValue)
  }
}

// 减少
const decrease = () => {
  if (isDecreaseDisabled.value) return
  
  const newValue = currentValue.value - props.step
  updateValue(newValue)
}

// 增加
const increase = () => {
  if (isIncreaseDisabled.value) return
  
  const newValue = currentValue.value + props.step
  updateValue(newValue)
}

// 处理输入
const handleInput = (event) => {
  const value = event.target.value
  updateValue(value)
}

// 处理失焦
const handleBlur = (event) => {
  isFocused.value = false
  // 失焦时重新格式化值
  updateValue(currentValue.value)
  emit('blur', event)
}

// 处理聚焦
const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}
</script>

<style scoped>
.custom-number-box {
  display: inline-flex;
  align-items: center;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  background: white;
  overflow: hidden;
  transition: all 0.3s ease;
}

.custom-number-box:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

/* 按钮样式 */
.number-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.number-btn:active {
  background: #e6e6e6;
}

.btn-icon {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
}

/* 禁用按钮 */
.btn-disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.5;
}

.btn-disabled:active {
  background: #f5f5f5;
}

.btn-disabled .btn-icon {
  color: #ccc;
}

/* 输入框样式 */
.number-input {
  border: none;
  outline: none;
  text-align: center;
  font-size: 28rpx;
  color: #000;
  background: transparent;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 8rpx;
  box-sizing: border-box;
}

.number-input:disabled {
  color: #999;
  cursor: not-allowed;
}

/* 禁用状态 */
.number-disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  cursor: not-allowed;
}

.number-disabled .number-btn {
  background: #f5f5f5;
  cursor: not-allowed;
}

.number-disabled .btn-icon {
  color: #ccc;
}

.number-disabled .number-input {
  background: transparent;
  color: #999;
}

/* 移除浏览器默认的数字输入框样式 */
.number-input::-webkit-outer-spin-button,
.number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.number-input[type="number"] {
  -moz-appearance: textfield;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .number-btn {
    width: 68rpx;
    height: 68rpx;
  }
  
  .btn-icon {
    font-size: 36rpx;
  }
  
  .number-input {
    font-size: 30rpx;
    height: 68rpx;
    line-height: 68rpx;
  }
}
</style>

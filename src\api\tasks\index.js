import request from '@/utils/request'

// api地址
const api = {
    list: '/app-api/tasks/list',
    receive: '/app-api/tasks/receive',
    detail: '/app-api/tasks/detail-v2',
    publishDetail: '/app-api/tasks/publish-detail',
    // 整改列表
    publishList: '/app-api/tasks/publish-list',
    // 提交巡查点 - 计划任务
    submit: '/app-api/tasks/points/submit-v2',
    // 提交巡查点 - 整改任务
    submitPublish: '/app-api/tasks/publish/submit',
    // 巡查下质控点列表
    locationPointsList: '/app-api/tasks/location/points-list',
    // 质控点详情
    pointsDetail: '/app-api/tasks/points',
    // 巡查点完成
    locationFinish: '/app-api/tasks/location/finish',
    // 巡查点验证
    locationVerify: '/app-api/tasks/location/verify',
}

/**
 * 获取任务列表
 * @param {Object} params
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.status - 任务状态（-1: 全部, 20: 待接受, 30: 待执行, 40: 进行中, 50: 已完成）
 */
export function getTasksList(params = {}) {
    // 处理请求参数
    const requestParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10
    }

    // 只有当状态不是"全部"时才添加状态参数
    if (params.status !== -1) {
        requestParams.status = params.status
    }

    return request.get(api.list, params, {load: false})
}

// 接受任务
export function receive(tasksId) {
    return request.post(api.receive, {tasksId: tasksId})
}

// 任务详情
export function detail(tasksId) {
    return request.get(api.detail, {tasksId: tasksId})
}

// 任务详情
export function publishDetail(tasksId) {
    return request.get(api.publishDetail, {tasksId: tasksId})
}

// 整改任务列表
export function publishList() {
    return request.get(api.publishList, null, {loading: false})
}

// 质控点提交
export function submit(data) {
    return request.post(api.submit, data)
}

// 质控点提交 - 整改任务
export function submitPublish(data) {
    return request.post(api.submitPublish, data)
}

// 根据巡查点ID获取质控点列表
export function getPointsListByLocationID(tasksLocationId) {
    return request.get(api.locationPointsList, {tasksLocationId: tasksLocationId})
}

// 质控点详情
export function getPointsDetail(id) {
    return request.get(api.pointsDetail, {taskPointsId: id})
}

// 巡查点完成
export function locationFinish(id) {
    return request.post(api.locationFinish, {id: id})
}

// 巡查点验证
export function locationVerify(data) {
    return request.post(api.locationVerify, data)
}
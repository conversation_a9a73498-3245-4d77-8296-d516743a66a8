<template>
  <button 
    class="custom-button" 
    :class="[
      `button-${type}`,
      `button-${size}`,
      { 
        'button-disabled': disabled,
        'button-loading': loading,
        'button-plain': plain,
        'button-round': round
      }
    ]"
    :disabled="disabled || loading"
    :style="buttonStyle"
    @click="handleClick"
  >
    <!-- 加载图标 -->
    <view v-if="loading" class="loading-icon">
      <text class="loading-spinner">⟳</text>
    </view>
    
    <!-- 图标 -->
    <view v-if="icon && !loading" class="button-icon">
      <text class="icon">{{ icon }}</text>
    </view>
    
    <!-- 文字内容 -->
    <view v-if="text || $slots.default" class="button-text">
      <slot>{{ text }}</slot>
    </view>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default', // default, primary, success, warning, error
    validator: (value) => ['default', 'primary', 'success', 'warning', 'error'].includes(value)
  },
  size: {
    type: String,
    default: 'normal', // mini, small, normal, large
    validator: (value) => ['mini', 'small', 'normal', 'large'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  plain: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: ''
  },
  bgColor: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

// 按钮样式
const buttonStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = props.width
  }
  
  if (props.height) {
    style.height = props.height
  }
  
  if (props.color) {
    style.color = props.color
  }
  
  if (props.bgColor) {
    style.backgroundColor = props.bgColor
  }
  
  return style
})

// 处理点击事件
const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  outline: none;
  position: relative;
  overflow: hidden;
}

/* 按钮类型样式 */
.button-default {
  background: #f5f5f5;
  color: #000;
  border-color: #d9d9d9;
}

.button-default:active {
  background: #e6e6e6;
}

.button-primary {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.button-primary:active {
  background: #096dd9;
}

.button-success {
  background: #52c41a;
  color: white;
  border-color: #52c41a;
}

.button-success:active {
  background: #389e0d;
}

.button-warning {
  background: #faad14;
  color: white;
  border-color: #faad14;
}

.button-warning:active {
  background: #d48806;
}

.button-error {
  background: #ff4d4f;
  color: white;
  border-color: #ff4d4f;
}

.button-error:active {
  background: #d9363e;
}

/* 朴素按钮样式 */
.button-plain.button-default {
  background: transparent;
  color: #000;
}

.button-plain.button-primary {
  background: transparent;
  color: #1890ff;
}

.button-plain.button-success {
  background: transparent;
  color: #52c41a;
}

.button-plain.button-warning {
  background: transparent;
  color: #faad14;
}

.button-plain.button-error {
  background: transparent;
  color: #ff4d4f;
}

/* 按钮尺寸 */
.button-mini {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  height: 48rpx;
}

.button-small {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  height: 60rpx;
}

.button-normal {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  height: 80rpx;
}

.button-large {
  padding: 20rpx 32rpx;
  font-size: 32rpx;
  height: 100rpx;
}

/* 圆角按钮 */
.button-round {
  border-radius: 50rpx;
}

/* 禁用状态 */
.button-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-disabled:active {
  transform: none;
  background: inherit !important;
}

/* 加载状态 */
.button-loading {
  cursor: not-allowed;
}

.loading-icon {
  margin-right: 8rpx;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
  font-size: 24rpx;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 图标 */
.button-icon {
  margin-right: 8rpx;
}

.icon {
  font-size: 24rpx;
}

/* 文字 */
.button-text {
  flex: 1;
  text-align: center;
}

/* 只有图标时的样式 */
.custom-button:not(:has(.button-text)) .button-icon {
  margin-right: 0;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .button-large {
    font-size: 30rpx;
    height: 88rpx;
  }
  
  .button-normal {
    font-size: 26rpx;
    height: 72rpx;
  }
}
</style>
